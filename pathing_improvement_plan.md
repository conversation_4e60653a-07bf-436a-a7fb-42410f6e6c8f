# Truck Pathing Improvement Plan

## Problem

The current path optimization for truck parking generates paths with abrupt changes and "spikes". This is caused by the path generation logic in `_generateOptimizedTrajectory()` which stitches together three distinct segments: a portion of the user's drawn path, a generated Bezier curve, and a final straight-line approach to the parking slot. This concatenation creates "seams" that result in sharp, unnatural angles.

## Revised Plan

The solution is to generate a single, smooth, continuous path from the start of the optimized parking maneuver to the final parking position. This will be achieved by refactoring the path generation logic.

### 1. Unify Path Generation

-   Modify `_createSimplifiedTrajectory()` to generate a single, unified array of waypoints that define the entire parking maneuver. This will eliminate the "seams" that cause the sharp angles.

### 2. Improve Curve Generation

-   Replace the simple quadratic Bezier curve in `_generateSmoothedCurve()` with a more sophisticated curve generation method, such as a cubic Bezier or a Catmull-Rom spline. This will create a smooth, natural-looking path that doesn't require a separate smoothing step, allowing for complex maneuvers like S-curves needed for proper alignment.

## Diagram of the Plan

```mermaid
graph TD
    subgraph "Current Problematic Flow"
        A[User Path] --> B[Slice User Path];
        C[Generate Bezier Curve] --> D{Concatenate Segments};
        E[Final Approach Points] --> D;
        B --> D;
        D --> F(Spiky Path);
    end

    subgraph "Proposed Improved Flow"
        G[User Path] --> H[Generate Key Waypoints for entire maneuver];
        H --> I[Create single, unified, smooth path array using better curve generation];
        I --> J(Smooth, Continuous Path);
    end
```

This revised approach directly addresses the issue by improving the fundamental path generation logic.