# Refactoring Plan: `truck.js`

## 1. Introduction

This document outlines a refactoring plan for `lib/game/entities/objects/truck.js`. The primary goal is to simplify its implementation by adopting the more direct and effective architectural patterns found in `ai-docs/harbor-captain/libs/game/entities/objects/ship.js`.

The analysis reveals that `truck.js` has grown overly complex due to a sophisticated but cumbersome movement and state management system. In contrast, `ship.js` achieves similar gameplay objectives with a much simpler, state-driven approach. This refactoring will focus on reducing complexity, improving maintainability, and aligning with ES5 compatibility, while ensuring all critical interactions with `lib/game/controllers/game-controller.js` are preserved.

## 2. Key Architectural Differences & Simplification Strategy

### `ship.js`'s Simplicity:

*   **State-Driven Logic:** Relies on a set of boolean flags (`isDestroyed`, `dockingAllowed`, `arriveToDock`) to control behavior. While numerous, they create explicit, readable states.
*   **Direct Movement Model:** Movement is governed by a simple `sailTo(x, y)` function. The entity turns towards a target and moves at a constant speed. Path-following is achieved by iterating through a `trajectory` array of points.
*   **Unified Update Loop:** A single `update()` function contains a clear sequence of checks and actions, making the flow of logic easy to follow.
*   **Focused Functionality:** The entity's responsibilities are limited to movement, collision, and docking.

### `truck.js`'s Complexity:

*   **Hybrid State Management:** Mixes boolean flags with a `truckState` string (`'none'`, `'parking'`, `'exited'`), adding a layer of complexity.
*   **Over-engineered Movement:** Implements a physics-like model with concepts of pivot points, acceleration, waypoint tolerance, and path smoothing. This adds significant overhead for a simple arcade-style game.
*   **Component-Based Ideas:** The code contains remnants of a component-based design that was not fully realized, leading to a scattered and inconsistent structure.
*   **Excessive Features:** Includes "nice-to-have" features like complex trajectory simplification, loop detection, and collision recovery, which are not core to the gameplay and can be simplified or removed.

### Refactoring Strategy:

The core strategy is to **replace the `truck.js` movement and state management systems with a simplified model inspired by `ship.js`**. We will strip out the complex physics and trajectory-handling logic in favor of a direct, state-driven approach.

## 3. Step-by-Step Refactoring Plan

### Step 1: Simplify State Management

We will replace the combination of `truckState` and boolean flags with a single, unified set of flags modeled after `ship.js`.

**Actions:**

1.  **Remove `truckState`:** Eliminate the `truckState` property and all logic that branches based on its value (`'parking'`, `'exit-ready'`, etc.).
2.  **Introduce `ship.js`-style Flags:** Add the following boolean properties to `truck.js` to manage its state explicitly:
    *   `isMoving`: `true` when following a trajectory.
    *   `isParking`: `true` when executing the final parking maneuver.
    *   `isParked`: `true` when successfully parked in a slot.
    *   `isLeaving`: `true` when exiting a parking slot to leave the screen.
    *   `isDestroyed`: `true` if the truck has crashed.
3.  **Refactor `update()` Logic:** Re-structure the `update()` function to check these flags in a clear, prioritized order.

```mermaid
graph TD
    A[Start] --> B{isDestroyed?};
    B -- Yes --> C[Do Nothing];
    B -- No --> D{isParking?};
    D -- Yes --> E[Run Parking Logic];
    D -- No --> F{isParked?};
    F -- Yes --> G[Handle Parked State];
    F -- No --> H{isMoving?};
    H -- Yes --> I[Follow Trajectory];
    H -- No --> J[Idle];
```

### Step 2: Overhaul Movement System

Replace the complex, physics-based movement with the direct `sailTo` model from `ship.js`.

**Actions:**

1.  **Remove Complex Properties:** Delete properties related to the old movement system:
    *   `rotationSpeed`, `pivotOffsetRatio`, `parkingRotationSpeed`, `truckRotationSpeed`, `truckAcceleration`, `truckMaxTurnAngle`, `truckMomentumFactor`, `pathSmoother`, `currentWaypointIndex`.
2.  **Implement `sailTo(x, y)`:** Add a `sailTo` function to `truck.js` that mirrors the functionality in `ship.js`. This function will set the truck's velocity and `targetAngle`.
    ```javascript
    // In truck.js
    sailTo: function (x, y) {
        this.targetAngle = Math.atan2(y - this.pos.y, x - this.pos.x);
        this.vel.x = this.speed * Math.cos(this.targetAngle);
        this.vel.y = this.speed * Math.sin(this.targetAngle);
        this.targetAngle += Math.PI / 2; // Adjust for sprite orientation
    },
    ```
3.  **Simplify Angle Updates:** In the `update()` function, use a simple `lerp` to smoothly rotate the truck towards the `targetAngle`, just as `ship.js` does.
4.  **Simplify Trajectory Following:** The `update()` loop will now simply check if it has reached the current target in the `trajectory` array. If so, it will call `sailTo` for the next point. This removes the need for waypoints, path smoothing, and loop detection.

### Step 3: Refactor Parking and Collision

Simplify the parking and collision logic to align with the new state model.

**Actions:**

1.  **Parking Logic:**
    *   When the user's drawn path snaps to a valid parking slot, the `trajectory` will be finalized with the slot's entry point as the last target.
    *   Upon reaching the final trajectory point, set `isParking = true` and `isMoving = false`.
    *   The parking maneuver will be a simple `sailTo` call to the center of the parking slot, followed by a rotation to the final parking angle.
    *   Once parked, set `isParked = true` and `isParking = false`.
2.  **Collision Handling:**
    *   The existing SAT-based collision detection (`sat.simpleShapeIntersect`) is effective and will be retained.
    *   Remove the complex `collisionCooldown` and `isInBuildingCollisionRecovery` logic.
    *   On collision with a building or another truck, set `isDestroyed = true`, stop all movement, and trigger the game over sequence in the `game-controller`. This is a much simpler failure state.

### Step 4: Address Dependencies from `game-controller.js`

Ensure all functions and properties called by `game-controller.js` are accounted for in the new design.

| `game-controller.js` Call | Refactoring Action in `truck.js` |
| :--- | :--- |
| `truck.onGameOver()` | Retain this function. It should set `isDestroyed = true` and stop all movement. |
| `truck.addScore()` | This is called on the controller, not the truck. No change needed. |
| `truck.beginParking()` | This function will be simplified. It will now set the final point of the trajectory to the parking slot and let the standard movement logic handle the approach. |
| `truck.onCollision()` | Retain this function. It will be simplified to set `isDestroyed = true` on fatal collisions. |
| `truck.onExitCollision()` | This can likely be removed, as the new state model is simpler. |
| Access to `truck.isParking`, `truck.isParked` | These properties will be part of the new state model, so no change is needed in the controller. |
| Access to `truck.shape`, `truck.vertices` | These are fundamental for collision detection and will be retained. |

## 4. ES5 Compatibility

All proposed changes are fully compatible with ES5. The plan avoids modern JavaScript syntax and relies on patterns available in the existing ImpactJS codebase. The refactoring simplifies the code, which inherently improves its robustness in an ES5 environment.

## 5. Conclusion

This refactoring plan will significantly simplify `truck.js`, making it more maintainable and performant. By adopting the proven, simpler patterns from `ship.js`, we can reduce technical debt and create a more robust foundation for future development.