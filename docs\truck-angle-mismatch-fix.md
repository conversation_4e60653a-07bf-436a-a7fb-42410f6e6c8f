# Truck Angle-Direction Mismatch Issue

## The Real Problem

The truck "slides" because after completing the path, it moves based on its current facing angle rather than its actual movement direction from the path.

```
Problem Scenario with Short Path:
=================================

1. User draws short path at 45° angle
2. Truck starts moving along path but only rotates partially
3. When path ends:
   - Truck angle: 20° (still rotating)
   - Path direction: 45° (actual movement)
   - Result: Truck moves at 20° instead of 45° = SLIDING!

    Path Direction (45°)
         ↗
        /
       /
    🚛 -----> Truck moves at 20° (wrong!)
    
The visual effect is the truck "sliding" sideways
```

## Root Cause Analysis

```mermaid
graph TD
    A[Path ends] --> B[hasCompletedTrajectoryButNotMovedOn]
    B --> C[moveInFacingDirection called]
    C --> D[Uses getDirectionFromAngle]
    D --> E[Returns direction based on truck.angle]
    E --> F[Truck moves in wrong direction!]
    
    style F fill:#ff6b6b,stroke:#c92a2a,stroke-width:3px
    
    G[What should happen] --> H[Use last path segment direction]
    H --> I[Continue in that direction]
    
    style I fill:#51cf66,stroke:#37b24d,stroke-width:3px
```

## Current Code Issue

```javascript
// In updateMovementState():
else if (this.hasCompletedTrajectoryButNotMovedOn()) {
    this.moveInFacingDirection(this.speed);  // ❌ Uses truck's angle!
    this.checkOffScreen(false);
}

// moveInFacingDirection uses:
moveInFacingDirection: function (speed) {
    var moveSpeed = speed || this.speed;
    this.vel.x = Math.cos(this.angle) * moveSpeed;  // ❌ Wrong angle!
    this.vel.y = Math.sin(this.angle) * moveSpeed;
}
```

## The Solution

We need to track the actual movement direction from the path and use that for continued movement:

```javascript
// Solution 1: Store last movement direction
updateTrajectoryMovement: function () {
    var targetPoint = this.getCurrentWaypoint();
    if (!targetPoint) {
        // Before finalizing, store the current velocity direction
        this.storeLastMovementDirection();
        this.finalizeTrajectoryMovement();
        return;
    }
    // ... rest of function
}

storeLastMovementDirection: function () {
    var speed = Math.sqrt(this.vel.x * this.vel.x + this.vel.y * this.vel.y);
    if (speed > 0.1) {
        this.lastMovementDirectionX = this.vel.x / speed;
        this.lastMovementDirectionY = this.vel.y / speed;
        this.lastMovementAngle = Math.atan2(this.vel.y, this.vel.x);
    }
}

// Solution 2: Use stored direction for continued movement
updateMovementState: function () {
    // ...
    else if (this.hasCompletedTrajectoryButNotMovedOn()) {
        // Use last movement direction, not facing direction
        this.moveInLastDirection(this.speed);
        this.checkOffScreen(false);
    }
}

moveInLastDirection: function (speed) {
    var moveSpeed = speed || this.speed;
    this.vel.x = this.lastMovementDirectionX * moveSpeed;
    this.vel.y = this.lastMovementDirectionY * moveSpeed;
    
    // Gradually align truck angle with movement direction
    this.targetAngle = this.lastMovementAngle;
}
```

## Alternative Solution: Use Final Path Segment

```javascript
// When completing trajectory, calculate direction from last two waypoints
finalizeTrajectoryMovement: function () {
    // Calculate direction from recent movement
    if (this.trajectory.length >= 0) {
        var pivotPoint = this.getPivotPoint();
        var lastWaypoint = this.getCurrentWaypoint() || this.lastCompletedWaypoint;
        
        if (lastWaypoint) {
            var dx = lastWaypoint.x - pivotPoint.x;
            var dy = lastWaypoint.y - pivotPoint.y;
            var dist = Math.sqrt(dx * dx + dy * dy);
            
            if (dist > 0.1) {
                this.finalMovementDirectionX = dx / dist;
                this.finalMovementDirectionY = dy / dist;
                this.finalMovementAngle = Math.atan2(dy, dx);
            }
        }
    }
    
    this.trajectoryComplete = true;
}
```

## Visual Comparison

### Before (Current Issue):
```
Frame 1-5: Path at 45°
    ↗ (Path direction)
   /
  /
🚛 (Truck angle: 20°)

Frame 6: Path ends
🚛 → (Moves at 20° - WRONG!)
```

### After (With Fix):
```
Frame 1-5: Path at 45°
    ↗ (Path direction)
   /
  /
🚛 (Truck angle: 20°)

Frame 6: Path ends
🚛 ↗ (Continues at 45° - CORRECT!)
   (Truck gradually rotates to match)
```

## Implementation Steps

1. **Add properties to track last movement direction**:
   - `lastMovementDirectionX`
   - `lastMovementDirectionY`
   - `lastMovementAngle`

2. **Store direction before path completion**:
   - In `updateTrajectoryMovement()` before calling `finalizeTrajectoryMovement()`
   - Or calculate from last waypoint segment

3. **Use stored direction for continued movement**:
   - Replace `moveInFacingDirection()` with `moveInLastDirection()`
   - Gradually align truck angle with movement direction

4. **Handle edge cases**:
   - Very short paths (< 2 waypoints)
   - Paths ending at parking slots
   - Collision responses

## Benefits

1. **Eliminates sliding**: Truck continues in correct direction
2. **Smooth visual**: Truck gradually aligns with movement
3. **Preserves game mechanics**: Exit behavior remains the same
4. **Works with all path lengths**: Especially fixes short paths