ig.module(
    'game.levels.level1'
)
.defines(function (){

Level1MapData = {
    id: 'level1', // Unique ID for this specific level data object
    title: 'The Dockyard', // Placeholder title
    image: "map-1", // Key to look up the image in mapImageList
    truckSpawnAreas: ['top10', 'topRight', 'right1', 'right2', 'right3', 'right4', 'right5'],
    buildings: [
        {
            name: 'building1', // Top Building
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 555.01, y: -1.19 }, { x: 1385.15, y: -2.37 }, { x: 1382.78, y: 246.30 }, { x: 556.20, y: 246.30 }] 
        },
        { 
            name: 'building2', // Lower Building
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 1087.49, y: 974.82 }, { x: 1884.42, y: 977.20 }, { x: 1890.35, y: 1078.81 }, { x: 1086.30, y: 1078.81 }]
        },
        {
            name: 'forklift-building1',
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 477.92, y: 310.71 }, { x: 741.20, y: 308.34 }, { x: 738.83, y: 426.56 }, { x: 474.37, y: 425.37 }]
        },
        { 
            name: 'forklift-building2',
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 1774.13, y: 1022.26 }, { x: 1692.30, y: 1020.70 }, { x: 1688.75, y: 823.43 }, { x: 1773.84, y: 824.61 }]
        },
        
        {
            name: 'box-building2',
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 1158.64, y: 901.30 }, { x: 1248.77, y: 901.30 }, { x: 1246.40, y: 974.45 }, { x: 1158.64, y: 974.45 }]
        },
        {
            name: 'crane',
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 559.75, y: 431.67 }, { x: 642.77, y: 450.65 }, { x: 596.52, y: 923.46 }, { x: 412.70, y: 879.58 }]
        },
        {
            name: 'containers',
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 657.00, y: 716.29 }, { x: 902.48, y: 721.04 }, { x: 903.67, y: 937.69 }, { x: 653.44, y: 936.50 }]
        },
        {
            name: 'building-container',
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 889.44, y: 966.52 }, { x: 964.15, y: 966.52 }, { x: 962.96, y: 1078.81 }, { x: 887.07, y: 1077.63 }]
        },
        {
            name: 'bush1', // Upper Bush
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 1417.17, y: 0.00 }, { x: 1555.92, y: 0.82 }, { x: 1556.52, y: 57.74 }, { x: 1416.22, y: 57.50 }]
        },
        {
            name: 'bush2', // Lower Bush (Near building 2)
            type: 1, // BUILDING (Treating as obstacle)
            vertices: [{ x: 1848.84, y: 887.07 }, { x: 1918.81, y: 885.88 }, { x: 1918.81, y: 983.94 }, { x: 1851.22, y: 981.57 }]
        },
        // --- Parking Slots ---
        {
            name: 'parking-slot-1-building1',
            type: 3, // PARKING_SLOT
            collidesWith: [4], // Only TRUCK type can collide with this
            vertices: [{ x: 814.73, y: 251.65 }, { x: 915.77, y: 252.11 }, { x: 915.53, y: 484.79 }, { x: 815.56, y: 486.21 }],
            color: GameColors.RED,
            colorIndicator: {
                anchor: { x: 0.5, y: 1 },
                anchorOffset: { x: 0, y: 0 }
            },
            // Swap the frontPoint and backPoint if the automatic calculation is incorrect.
            swapPoints: true
            // To manually setup the points, uncomment the following lines and adjust the coordinates as needed.
            // frontPoint: { x: 1585.89, y: 741.57 },
            // backPoint: { x: 1586.68, y: 972.52 }
        },
        {
            name: 'parking-slot-2-building1',
            type: 3, // PARKING_SLOT
            collidesWith: [4], // Only TRUCK type can collide with this
            vertices: [{ x: 1027.60, y: 252.24 }, { x: 1127.45, y: 252.11 }, { x: 1126.62, y: 485.38 }, { x: 1026.85, y: 485.62 }],
            color: GameColors.BLUE,
            colorIndicator: {
                anchor: { x: 0.5, y: 1 },
                anchorOffset: { x: 0, y: 0 }
            },
            // Swap the frontPoint and backPoint if the automatic calculation is incorrect.
            swapPoints: true
            // To manually setup the points, uncomment the following lines and adjust the coordinates as needed.
            // frontPoint: { x: 1585.89, y: 741.57 },
            // backPoint: { x: 1586.68, y: 972.52 }
        },
        {
            name: 'parking-slot-1-building2',
            type: 3, // PARKING_SLOT
            collidesWith: [4], // Only TRUCK type can collide with this
            vertices: [{ x: 1324.08, y: 741.63 }, { x: 1424.72, y: 741.50 }, { x: 1424.68, y: 970.82 }, { x: 1326.49, y: 970.26 }],
            color: GameColors.GREEN,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
        },
        {
            name: 'parking-slot-2-building2',
            type: 3, // PARKING_SLOT
            collidesWith: [4], // Only TRUCK type can collide with this
            vertices: [{ x: 1536.75, y: 741.63 }, { x: 1635.02, y: 741.50 }, { x: 1636.57, y: 972.40 }, { x: 1536.79, y: 972.63 }],
            color: GameColors.YELLOW,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            },
            // Swap the frontPoint and backPoint if the automatic calculation is incorrect.
            swapPoints: true
            // To manually setup the points, uncomment the following lines and adjust the coordinates as needed.
            // frontPoint: { x: 1585.89, y: 741.57 },
            // backPoint: { x: 1586.68, y: 972.52 }
        }
        // Add more collision objects here as needed
    ]
};

});
